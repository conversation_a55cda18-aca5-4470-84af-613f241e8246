/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * API Index
         * @description Returns API information and available endpoints
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description API Index Information */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ApiIndex"];
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Health Check
         * @description Returns the health status of the API
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Health Check Response */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["HealthCheck"];
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/apiClient": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get API Client
         * @description Returns the generated API client with TypeScript types and usage examples
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description API Client Information */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ApiClientResponse"];
                    };
                };
                /** @description Internal server error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ApiClientError"];
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/auth/signup": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["SignUpRequest"];
                };
            };
            responses: {
                /** @description User registration with workspace creation */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SignUpResponse"];
                    };
                };
                /** @description Bad request - validation errors */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description User already exists */
                409: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/auth/signin": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["SignInRequest"];
                };
            };
            responses: {
                /** @description Successful authentication. Returns user information, authentication tokens, default workspace details, and user profile within that workspace. */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SignInResponse"];
                    };
                };
                /** @description Authentication failed - invalid email or password */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description User account exists but has no access to any workspace, or associated workspace not found */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/auth/signout": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description User logout - refresh token cookie is automatically cleared */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SignOutResponse"];
                    };
                };
                /** @description Bad request */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/auth/refresh": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Token refreshed successfully. Returns refreshed authentication tokens along with user information, default workspace details, and user profile within that workspace. Refresh token is automatically updated in httpOnly cookie. */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["RefreshResponse"];
                    };
                };
                /** @description Invalid, expired, or missing refresh token in cookies */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description User account exists but has no access to any workspace, or associated workspace not found */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/auth/forgot-password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["ForgotPasswordRequest"];
                };
            };
            responses: {
                /** @description Password reset request */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ForgotPasswordResponse"];
                    };
                };
                /** @description Bad request */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/auth/reset-password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["ResetPasswordRequest"];
                };
            };
            responses: {
                /** @description Password reset confirmation */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ResetPasswordResponse"];
                    };
                };
                /** @description Bad request - invalid token or passwords don't match */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/auth/callback": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: {
            parameters: {
                query?: {
                    token?: string;
                    type?: string;
                    accessToken?: string;
                    refreshToken?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Redirect to dashboard or error page */
                302: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Invalid callback parameters */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/profile": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get current user profile
         * @description Get current user profile with workspace context
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description User profile retrieved successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["UserProfileResponse"];
                    };
                };
                /** @description Authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description User profile not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update user profile
         * @description Update user profile information (centralized endpoint for all profile updates)
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["UpdateProfileRequest"];
                };
            };
            responses: {
                /** @description Profile updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["UpdateProfileResponse"];
                    };
                };
                /** @description Invalid request data */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description User profile not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/verify-email": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Verify new email address
         * @description Verify new email address (called after email change via PUT /users/profile)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["VerifyEmailRequest"];
                };
            };
            responses: {
                /** @description Email verification result */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["VerifyEmailResponse"];
                    };
                };
                /** @description Invalid verification token or email */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/files/upload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "multipart/form-data": components["schemas"]["UploadFileFormData"];
                };
            };
            responses: {
                /** @description File uploaded successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["UploadFileResponse"];
                    };
                };
                /** @description Bad request - validation errors or file processing failed */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized - authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Payload too large - file size exceeds limit */
                413: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/files/{file_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    fileId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description File metadata and download URL */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["GetFileResponse"];
                    };
                };
                /** @description Unauthorized - authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Forbidden - insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description File not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    fileId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description File deleted successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["DeleteResponse"];
                    };
                };
                /** @description Unauthorized - authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Forbidden - insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description File not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/listings": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: {
            parameters: {
                query?: {
                    page?: string;
                    limit?: string;
                    status?: string;
                    industry?: string;
                    assignedTo?: string;
                    minPrice?: string;
                    maxPrice?: string;
                    location?: string;
                    sortBy?: "created_at" | "updated_at" | "asking_price" | "business_name" | "date_listed" | "days_listed";
                    sortOrder?: "asc" | "desc";
                    search?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description List of listings retrieved successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ListingListResponse"];
                    };
                };
                /** @description Unauthorized - authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["CreateListingRequest"];
                };
            };
            responses: {
                /** @description Listing created successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SingleListingResponse"];
                    };
                };
                /** @description Bad request - validation errors */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized - authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/listings/{listingId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: {
            parameters: {
                query?: {
                    includeDetails?: "true" | "false";
                };
                header?: never;
                path: {
                    listingId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Listing retrieved successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SingleListingResponse"];
                    };
                };
                /** @description Unauthorized - authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Forbidden - insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Listing not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    listingId: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["UpdateListingRequest"];
                };
            };
            responses: {
                /** @description Listing updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SingleListingResponse"];
                    };
                };
                /** @description Bad request - validation errors */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized - authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Forbidden - insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Listing not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    listingId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Listing deleted successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["DeleteResponse"];
                    };
                };
                /** @description Unauthorized - authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Forbidden - insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Listing not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/listings/draft": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Save Listing as Draft
         * @description Save a listing with incomplete data as a draft. All fields are optional and data is stored in the _draft column.
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["SaveDraftListingRequest"];
                };
            };
            responses: {
                /** @description Draft listing saved successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SingleListingResponse"];
                    };
                };
                /** @description Bad request - validation errors */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized - authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/listings/{listingId}/draft": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update Draft Listing
         * @description Update an existing draft listing. Only works for listings with status 'draft'.
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    listingId: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["SaveDraftListingRequest"];
                };
            };
            responses: {
                /** @description Draft listing updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SingleListingResponse"];
                    };
                };
                /** @description Bad request - validation errors or listing is not a draft */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized - authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Forbidden - insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Listing not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/listings/bulk/csv": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Import CSV Listings Data
         * @description Parse and import CSV file containing business listings data. The CSV file is processed entirely in memory (not saved to disk), validated, and valid records are saved to the database. Returns detailed results including successful and failed imports.
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "multipart/form-data": components["schemas"]["CSVUploadRequest"];
                };
            };
            responses: {
                /** @description CSV bulk import completed successfully - valid records saved to database */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["BulkCreateResponse"];
                    };
                };
                /** @description Bad request - validation errors or invalid CSV format */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized - authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Payload too large - file size exceeds limit */
                413: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/logs/by-path": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get API logs by specific path
         * @description Retrieve API logs filtered by a specific path with enhanced performance optimization. Requires x-api-key header for authentication.
         */
        get: {
            parameters: {
                query: {
                    /** @description The exact path to filter logs by (e.g., '/v1/auth/signin') */
                    path: string;
                    page?: number;
                    limit?: number;
                    /** @description HTTP method filter */
                    method?: string;
                    /** @description HTTP status code filter */
                    statusCode?: number | null;
                    /** @description Filter logs from this date */
                    fromDate?: string;
                    /** @description Filter logs to this date */
                    toDate?: string;
                };
                header: {
                    /** @description API key for logs access */
                    "x-api-key": string;
                };
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description API logs filtered by path */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["LogsByPathResponse"];
                    };
                };
                /** @description Validation error */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Unauthorized - Invalid or missing API key */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Internal server error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/logs/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get API log details
         * @description Retrieve detailed information for a specific API log entry. Requires x-api-key header for authentication.
         */
        get: {
            parameters: {
                query?: never;
                header: {
                    /** @description API key for logs access */
                    "x-api-key": string;
                };
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description API log details */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["LogDetail"];
                    };
                };
                /** @description Unauthorized - Invalid or missing API key */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Log not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Internal server error */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/workspaces/current": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get current workspace
         * @description Get the current workspace information
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Current workspace retrieved successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SingleWorkspaceResponse"];
                    };
                };
                /** @description Authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Workspace not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update current workspace
         * @description Update the current workspace information
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["UpdateWorkspaceRequest"];
                };
            };
            responses: {
                /** @description Workspace updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["WorkspaceUpdateResponse"];
                    };
                };
                /** @description Invalid request data */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Workspace not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/workspaces/invitations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List workspace invitations
         * @description Get all invitations for the current workspace
         */
        get: {
            parameters: {
                query?: {
                    page?: string;
                    limit?: string;
                    status?: string;
                    role?: "owner" | "admin" | "member" | "viewer";
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Workspace invitations retrieved successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["WorkspaceInvitationListResponse"];
                    };
                };
                /** @description Authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        /**
         * Create workspace invitation
         * @description Send an invitation to join the workspace
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["CreateWorkspaceInvitationRequest"];
                };
            };
            responses: {
                /** @description Workspace invitation created successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SingleWorkspaceInvitationResponse"];
                    };
                };
                /** @description Invalid request data */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description User already invited or exists in workspace */
                409: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/workspaces/invitations/{invitation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update workspace invitation
         * @description Update an existing workspace invitation
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    invitationId: string;
                };
                cookie?: never;
            };
            requestBody?: {
                content: {
                    "application/json": components["schemas"]["UpdateWorkspaceInvitationRequest"];
                };
            };
            responses: {
                /** @description Workspace invitation updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SingleWorkspaceInvitationResponse"];
                    };
                };
                /** @description Invalid request data */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Invitation not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        /**
         * Delete workspace invitation
         * @description Cancel/delete a workspace invitation
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    invitationId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Workspace invitation deleted successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["DeleteResponse"];
                    };
                };
                /** @description Authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Invitation not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/v1/workspaces/invitations/{invitation_id}/resend": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Resend workspace invitation
         * @description Resend a workspace invitation email
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    invitationId: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Workspace invitation resent successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["SingleWorkspaceInvitationResponse"];
                    };
                };
                /** @description Authentication required */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Insufficient permissions */
                403: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Invitation not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Rate limit exceeded */
                429: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        ApiIndex: {
            message: string;
            version: string;
            documentation: string;
            endpoints: {
                health: string;
                docs: string;
                reference: string;
            };
        };
        HealthCheck: {
            status: string;
            timestamp: string;
            uptime: number;
            version: string;
        };
        ApiClientResponse: {
            version: string;
            client: string;
            types: string;
            usage: {
                installation: string;
                basicUsage: string;
                authentication: string;
                examples: {
                    title: string;
                    code: string;
                }[];
            };
        };
        ApiClientError: {
            error: string;
            message: string;
        };
        User: {
            /** @description Unique user identifier from Supabase Auth */
            id: string;
            /** @description User's email address */
            email: string;
            /** @description ISO timestamp when the user was created */
            createdAt: string;
        };
        BaseWorkspace: {
            companyName: string;
            /**
             * @default team
             * @enum {string}
             */
            companyType: "individual" | "team" | "firm";
            /**
             * @default trial
             * @enum {string}
             */
            subscriptionPlan: "trial" | "basic" | "pro" | "enterprise";
            domain?: string;
            logoUrl?: string;
            /** @default #3B82F6 */
            primaryColor: string;
            address?: string;
            phone?: string;
            /** Format: uri */
            website?: string;
            licenseNumber?: string;
            specialties?: string[];
            targetMarkets?: string[];
            /**
             * @default trial
             * @enum {string}
             */
            status: "trial" | "active" | "suspended" | "cancelled";
            /** @default false */
            onboardingCompleted: boolean;
            /** @default 1 */
            onboardingStep: number;
        };
        Workspace: components["schemas"]["BaseWorkspace"] & {
            /** Format: uuid */
            id: string;
            trialEndsAt?: string;
            createdAt: string;
            updatedAt: string;
        };
        BaseUserProfile: {
            /** Format: uuid */
            workspaceId?: string;
            /** Format: uuid */
            userId?: string;
            /** Format: email */
            email: string;
            firstName?: string;
            lastName?: string;
            displayName?: string;
            bio?: string;
            /** @enum {string} */
            role?: "owner" | "admin" | "member" | "viewer";
            phone?: string;
            licenseNumber?: string;
            avatarUrl?: string;
            specialties?: string[];
            /** @default true */
            isActive: boolean;
            preferences?: {
                [key: string]: unknown;
            };
        };
        UserProfileResponse: components["schemas"]["BaseUserProfile"] & {
            /** Format: uuid */
            id: string;
            invitedAt?: string;
            joinedAt?: string;
            /** Format: uuid */
            invitedBy?: string;
            lastLoginAt?: string;
            createdAt: string;
            updatedAt: string;
        };
        SignUpResponse: {
            user: components["schemas"]["User"];
            workspace: components["schemas"]["Workspace"];
            profile: components["schemas"]["UserProfileResponse"];
            /** @description Authentication session (refresh token stored in httpOnly cookie) */
            session: {
                /** @description JWT access token for API authentication */
                access_token: string;
                /** @description Unix timestamp when the access token expires */
                expires_at: number;
            } | null;
        };
        SignUpRequest: {
            /** Format: email */
            email: string;
            /** @description Password (minimum 6 characters) */
            password: string;
            /** @description Password (minimum 6 characters) */
            confirmPassword: string;
            firstName: string;
            lastName: string;
            companyName: string;
            /** @enum {string} */
            companyType: "individual" | "team" | "firm";
            phone?: string;
            licenseNumber?: string;
            /** Format: uri */
            website?: string;
            address?: string;
            termsAccepted: boolean;
            marketingConsent?: boolean;
        };
        SignInResponse: {
            user: components["schemas"]["User"] & unknown;
            /** @description Authentication session (refresh token stored in httpOnly cookie) */
            session: {
                /** @description JWT access token for API authentication */
                accessToken: string;
                /** @description Unix timestamp when the access token expires */
                expiresAt: number;
            };
            workspace: components["schemas"]["Workspace"] & unknown;
            profile: components["schemas"]["UserProfileResponse"] & unknown;
        };
        /** @example {
         *       "email": "<EMAIL>",
         *       "password": "securePassword123"
         *     } */
        SignInRequest: {
            /**
             * Format: email
             * @description User's email address
             */
            email: string;
            /** @description Password (minimum 6 characters) */
            password: string;
        };
        SignOutResponse: {
            success: boolean;
        };
        RefreshResponse: {
            /** @description JWT access token for API authentication */
            accessToken: string;
            /** @description Unix timestamp when the access token expires */
            expiresAt: number;
            user: components["schemas"]["User"] & unknown;
            workspace: components["schemas"]["Workspace"] & unknown;
            profile: components["schemas"]["UserProfileResponse"] & unknown;
        };
        ForgotPasswordResponse: {
            success: boolean;
            message: string;
        };
        ForgotPasswordRequest: {
            /** Format: email */
            email: string;
        };
        ResetPasswordResponse: {
            success: boolean;
        };
        ResetPasswordRequest: {
            /** @description Authentication token */
            token: string;
            /** @description Password (minimum 6 characters) */
            newPassword: string;
            /** @description Password (minimum 6 characters) */
            confirmPassword: string;
        };
        UpdateProfileResponse: {
            id: string;
            workspaceId: string;
            email: string;
            firstName: string;
            lastName: string;
            role: string;
            phone?: string;
            licenseNumber?: string;
            bio?: string;
            avatarUrl?: string;
            specialties: string[];
            isActive: boolean;
            preferences?: unknown;
            updatedAt: string;
            operationsCompleted: {
                profileUpdated: boolean;
                passwordChanged?: boolean;
                emailChangeInitiated?: boolean;
                emailVerificationSent?: boolean;
                avatarUpdated?: boolean;
            };
            requiresReauth?: boolean;
            warnings?: string[];
        };
        UserPreferences: {
            notifications?: {
                emailNotifications: boolean;
                pushNotifications: boolean;
                listingUpdates: boolean;
                teamUpdates: boolean;
                systemUpdates: boolean;
            };
            display?: {
                timezone: string;
                dateFormat: string;
                currency: string;
                language: string;
            };
            privacy?: {
                /** @enum {string} */
                profileVisibility: "team" | "public" | "private";
                /** @enum {string} */
                contactVisibility: "team" | "public" | "private";
            };
        };
        UpdateProfileRequest: {
            firstName?: string;
            lastName?: string;
            displayName?: string;
            bio?: string;
            phone?: string;
            licenseNumber?: string;
            /** Format: uri */
            avatarUrl?: string;
            specialties?: string[];
            preferences?: components["schemas"]["UserPreferences"];
        };
        VerifyEmailResponse: {
            success: boolean;
            message: string;
            emailUpdated: boolean;
        };
        VerifyEmailRequest: {
            token: string;
            /** Format: email */
            email: string;
        };
        BaseFile: {
            /** Format: uuid */
            workspaceId: string;
            /** Format: uuid */
            uploadedBy: string;
            fileName: string;
            originalName: string;
            mimeType: string;
            fileSize: number;
            /** @enum {string} */
            fileType: "document" | "image" | "video" | "audio" | "other";
            storagePath: string;
            entityType?: string;
            /** Format: uuid */
            entityId?: string;
            /** @default false */
            isPublic: boolean;
            metadata?: {
                [key: string]: unknown;
            };
        };
        File: components["schemas"]["BaseFile"] & {
            /** Format: uuid */
            id: string;
            createdAt: string;
            updatedAt: string;
            signedUrl?: string;
            canDownload?: boolean;
            canDelete?: boolean;
            canUpdate?: boolean;
        };
        ProcessingStatus: {
            /** @enum {string} */
            status: "pending" | "processing" | "completed" | "failed";
            progress?: number;
            estimatedCompletion?: string;
        };
        UploadFileResponse: {
            success: boolean;
            file: components["schemas"]["File"];
            processing?: components["schemas"]["ProcessingStatus"];
        };
        UploadFileRequest: {
            /** @enum {string} */
            fileType: "document" | "image" | "video" | "audio" | "other";
            entityType?: string;
            /** Format: uuid */
            entityId?: string;
            /** @default false */
            isPublic: boolean;
        };
        UploadFileFormData: components["schemas"]["UploadFileRequest"] & {
            /** @description The file to upload */
            file?: unknown;
        };
        GetFileResponse: components["schemas"]["BaseFile"] & {
            /** Format: uuid */
            id: string;
            createdAt: string;
            updatedAt: string;
            signedUrl?: string;
            canDownload?: boolean;
            canDelete?: boolean;
            canUpdate?: boolean;
        };
        DeleteResponse: {
            success: boolean;
            message: string;
            /** Format: uuid */
            deletedId: string;
            deletedAt: string;
        };
        BaseListingDetails: {
            businessDescription?: string;
            briefDescription?: string;
            financialDetails?: {
                revenue2023?: number;
                ebitda?: number;
                assetsIncluded?: string[];
                inventoryValue?: number;
                additionalFinancialInfo?: {
                    [key: string]: unknown;
                };
            };
            operations?: {
                businessModel?: string;
                keyFeatures?: string[];
                competitiveAdvantages?: string[];
                operationalDetails?: {
                    [key: string]: unknown;
                };
            };
            growthOpportunities?: string[];
            reasonForSale?: string;
            trainingPeriod?: string;
            supportType?: string;
            /** @default false */
            financingAvailable: boolean;
            equipmentHighlights?: string[];
            supplierRelationships?: string;
            /** @enum {string} */
            realEstateStatus?: "owned" | "leased" | "included" | "not_included" | "negotiable";
            leaseDetails?: {
                leaseTerms?: string;
                monthlyRent?: number;
                /** Format: date */
                leaseExpiration?: string;
                renewalOptions?: string;
                landlordInfo?: {
                    [key: string]: unknown;
                };
            };
        };
        BaseListing: {
            businessName: string;
            industry: string;
            /** Format: uuid */
            assignedTo?: string;
            askingPrice?: number;
            cashFlowSde?: number | null;
            annualRevenue?: number;
            /** @default draft */
            status: string;
            generalLocation?: string;
            yearEstablished?: number;
            employees?: number | null;
            ownerHoursWeek?: number | null;
            /** Format: date */
            dateListed?: string;
            title?: string;
            description?: string;
            price?: number;
            address?: string;
            city?: string;
            state?: string;
            zipCode?: string;
            propertyType?: string;
            squareFootage?: number | null;
            lotSize?: number | null;
            yearBuilt?: number;
            bedrooms?: number | null;
            bathrooms?: number | null;
            /** @default business_sale */
            listingType: string;
            /** @default all */
            teamVisibility: string;
            internalNotes?: unknown[];
            photos?: string[];
            documents?: string[];
            featuredPhoto?: string;
            /** Format: uri */
            virtualTourUrl?: string;
            mlsNumber?: string;
            /** Format: date */
            listingDate?: string;
            /** Format: date */
            expirationDate?: string;
            daysOnMarket?: number | null;
        };
        ListingResponse: components["schemas"]["BaseListing"] & {
            /** Format: uuid */
            id: string;
            /** Format: uuid */
            workspaceId: string;
            /** Format: uuid */
            createdBy: string;
            daysListed?: number;
            createdAt: string;
            updatedAt: string;
            details?: components["schemas"]["BaseListingDetails"];
            createdByName?: string;
            assignedToName?: string;
        };
        Pagination: {
            page: number;
            limit: number;
            total: number;
            pages: number;
        };
        ListingListResponse: {
            success: boolean;
            data: components["schemas"]["ListingResponse"][];
            pagination: components["schemas"]["Pagination"];
        };
        SingleListingResponse: {
            success: boolean;
            data: components["schemas"]["ListingResponse"];
        };
        CreateListingRequest: {
            businessName: string;
            industry: string;
            /** Format: uuid */
            assignedTo?: string;
            askingPrice?: number;
            cashFlowSde?: number | null;
            annualRevenue?: number;
            /** @default draft */
            status: string;
            generalLocation?: string;
            yearEstablished?: number;
            employees?: number | null;
            ownerHoursWeek?: number | null;
            /** Format: date */
            dateListed?: string;
            title?: string;
            description?: string;
            price?: number;
            address?: string;
            city?: string;
            state?: string;
            zipCode?: string;
            propertyType?: string;
            squareFootage?: number | null;
            lotSize?: number | null;
            yearBuilt?: number;
            bedrooms?: number | null;
            bathrooms?: number | null;
            /** @default business_sale */
            listingType: string;
            /** @default all */
            teamVisibility: string;
            internalNotes?: unknown[];
            photos?: string[];
            documents?: string[];
            featuredPhoto?: string;
            /** Format: uri */
            virtualTourUrl?: string;
            mlsNumber?: string;
            /** Format: date */
            listingDate?: string;
            /** Format: date */
            expirationDate?: string;
            details?: components["schemas"]["BaseListingDetails"];
        };
        SaveDraftListingRequest: {
            /** Format: uuid */
            assignedTo?: string;
            askingPrice?: number;
            cashFlowSde?: number | null;
            annualRevenue?: number;
            /** @default draft */
            status: string;
            generalLocation?: string;
            yearEstablished?: number;
            employees?: number | null;
            ownerHoursWeek?: number | null;
            /** Format: date */
            dateListed?: string;
            title?: string;
            description?: string;
            price?: number;
            address?: string;
            city?: string;
            state?: string;
            zipCode?: string;
            propertyType?: string;
            squareFootage?: number | null;
            lotSize?: number | null;
            yearBuilt?: number;
            bedrooms?: number | null;
            bathrooms?: number | null;
            /** @default business_sale */
            listingType: string;
            /** @default all */
            teamVisibility: string;
            internalNotes?: unknown[];
            photos?: string[];
            documents?: string[];
            featuredPhoto?: string;
            /** Format: uri */
            virtualTourUrl?: string;
            mlsNumber?: string;
            /** Format: date */
            listingDate?: string;
            /** Format: date */
            expirationDate?: string;
            businessName?: string;
            industry?: string;
            details?: components["schemas"]["BaseListingDetails"];
        };
        UpdateListingRequest: {
            businessName?: string;
            industry?: string;
            /** Format: uuid */
            assignedTo?: string;
            askingPrice?: number;
            cashFlowSde?: number | null;
            annualRevenue?: number;
            /** @default draft */
            status: string;
            generalLocation?: string;
            yearEstablished?: number;
            employees?: number | null;
            ownerHoursWeek?: number | null;
            /** Format: date */
            dateListed?: string;
            title?: string;
            description?: string;
            price?: number;
            address?: string;
            city?: string;
            state?: string;
            zipCode?: string;
            propertyType?: string;
            squareFootage?: number | null;
            lotSize?: number | null;
            yearBuilt?: number;
            bedrooms?: number | null;
            bathrooms?: number | null;
            /** @default business_sale */
            listingType: string;
            /** @default all */
            teamVisibility: string;
            internalNotes?: unknown[];
            photos?: string[];
            documents?: string[];
            featuredPhoto?: string;
            /** Format: uri */
            virtualTourUrl?: string;
            mlsNumber?: string;
            /** Format: date */
            listingDate?: string;
            /** Format: date */
            expirationDate?: string;
            details?: components["schemas"]["BaseListingDetails"];
            /** @description Reason for status change (used when updating status) */
            reason?: string;
            /** @description Additional notes for status change (used when updating status) */
            notes?: string;
        };
        BulkCreateResponse: {
            success: boolean;
            data: {
                created: components["schemas"]["ListingResponse"][];
                failed: {
                    index: number;
                    error: string;
                    data: {
                        [key: string]: unknown;
                    };
                }[];
            };
        };
        CSVUploadRequest: {
            /** @description CSV file containing listing data for import */
            file?: unknown;
        };
        LogItem: {
            /** Format: uuid */
            id: string;
            method: string;
            url: string;
            path: string;
            statusCode?: number;
            duration?: number;
            userAgent?: string;
            ipAddress?: string;
            /** Format: uuid */
            userId?: string;
            /** Format: uuid */
            workspaceId: string;
            errorMessage?: string;
            createdAt: string;
            requestHeaders?: {
                [key: string]: string;
            } | null;
            requestBody?: unknown;
            responseBody?: unknown;
            responseHeaders?: {
                [key: string]: string;
            } | null;
        };
        LogsByPathResponse: {
            data: components["schemas"]["LogItem"][];
            pagination: components["schemas"]["Pagination"];
            summary: {
                path: string;
                totalRequests: number;
                method?: string;
                statusCode?: number;
            };
        };
        BaseApiLog: {
            /** Format: uuid */
            workspaceId: string;
            /** Format: uuid */
            userId?: string;
            method: string;
            url: string;
            path: string;
            statusCode?: number;
            duration?: number;
            userAgent?: string;
            ipAddress?: string;
            errorMessage?: string;
            requestHeaders?: {
                [key: string]: string;
            } | null;
            requestBody?: unknown;
            responseBody?: unknown;
            responseHeaders?: {
                [key: string]: string;
            } | null;
        };
        LogDetail: components["schemas"]["BaseApiLog"] & {
            /** Format: uuid */
            id: string;
            createdAt: string;
            updatedAt: string;
        };
        SingleWorkspaceResponse: {
            success: boolean;
            data: components["schemas"]["Workspace"];
        };
        WorkspaceUpdateResponse: {
            success: boolean;
            data: components["schemas"]["Workspace"];
            updatedAt: string;
            changesApplied?: string[];
        };
        UpdateWorkspaceRequest: {
            companyName?: string;
            /**
             * @default team
             * @enum {string}
             */
            companyType: "individual" | "team" | "firm";
            /**
             * @default trial
             * @enum {string}
             */
            subscriptionPlan: "trial" | "basic" | "pro" | "enterprise";
            domain?: string;
            logoUrl?: string;
            /** @default #3B82F6 */
            primaryColor: string;
            address?: string;
            phone?: string;
            /** Format: uri */
            website?: string;
            licenseNumber?: string;
            specialties?: string[];
            targetMarkets?: string[];
            /**
             * @default trial
             * @enum {string}
             */
            status: "trial" | "active" | "suspended" | "cancelled";
            /** @default false */
            onboardingCompleted: boolean;
            /** @default 1 */
            onboardingStep: number;
        };
        BaseWorkspaceInvitation: {
            /** Format: uuid */
            workspaceId: string;
            /** Format: email */
            email: string;
            /** @enum {string} */
            role: "owner" | "admin" | "member" | "viewer";
            /** Format: uuid */
            invitedBy: string;
        };
        WorkspaceInvitation: components["schemas"]["BaseWorkspaceInvitation"] & {
            /** Format: uuid */
            id: string;
            status?: string;
            expiresAt?: string;
            createdAt: string;
            updatedAt: string;
            canResend?: boolean;
            isExpired?: boolean;
        };
        WorkspaceInvitationListResponse: {
            success: boolean;
            data: components["schemas"]["WorkspaceInvitation"][];
            pagination: components["schemas"]["Pagination"];
        };
        SingleWorkspaceInvitationResponse: {
            success: boolean;
            data: components["schemas"]["WorkspaceInvitation"];
        };
        CreateWorkspaceInvitationRequest: {
            /** Format: email */
            email: string;
            /** @enum {string} */
            role: "owner" | "admin" | "member" | "viewer";
            /** Format: uri */
            redirectTo: string;
        };
        UpdateWorkspaceInvitationRequest: {
            /** @enum {string} */
            role?: "owner" | "admin" | "member" | "viewer";
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export type operations = Record<string, never>;
