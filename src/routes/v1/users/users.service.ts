import { HTTPException } from "hono/http-exception";
import db from "@/db";
import { userProfiles, files, user as usersTable } from "@/db/schema";
import { eq, and } from "drizzle-orm";
// co-located types for service
export interface UserPreferences {
  notifications: {
    email_notifications: boolean;
    push_notifications: boolean;
    listing_updates: boolean;
    team_updates: boolean;
    system_updates: boolean;
  };
  display: {
    timezone: string;
    date_format: string;
    currency: string;
    language: string;
  };
  privacy: {
    profile_visibility: "team" | "public" | "private";
    contact_visibility: "team" | "public" | "private";
  };
}

export interface UpdateProfileData {
  // Basic Information
  firstName?: string;
  lastName?: string;
  phone?: string;
  licenseNumber?: string;
  bio?: string;
  specialties?: string[];
  avatarFileId?: string;

  // Security Updates
  currentPassword?: string;
  newPassword?: string;
  confirmPassword?: string;
  newEmail?: string;

  // Preferences
  preferences?: {
    notifications?: Partial<UserPreferences["notifications"]>;
    display?: Partial<UserPreferences["display"]>;
    privacy?: Partial<UserPreferences["privacy"]>;
  };
}

export interface OperationsCompleted {
  profileUpdated: boolean;
  passwordChanged?: boolean;
  emailChangeInitiated?: boolean;
  emailVerificationSent?: boolean;
  avatarUpdated?: boolean;
}

export class UsersService {
  static async getUserProfile(userId: string, workspaceId: string) {
    // Get user profile with avatar URL if available
    const [profile] = await db
      .select({
        id: userProfiles.id,
        workspace_id: userProfiles.organizationId,
        email: usersTable.email,
        first_name: userProfiles.firstName,
        last_name: userProfiles.lastName,
        role: usersTable.role,
        phone: userProfiles.phone,
        license_number: userProfiles.licenseNumber,
        bio: userProfiles.bio,
        avatar_url: usersTable.image,
        specialties: userProfiles.specialties,
        is_active: userProfiles.isActive,
        invited_at: userProfiles.invitedAt,
        joined_at: userProfiles.joinedAt,
        invited_by: userProfiles.invitedBy,
        preferences: userProfiles.preferences,
        last_login_at: userProfiles.lastLoginAt,
        created_at: userProfiles.createdAt,
        updated_at: userProfiles.updatedAt,
      })
      .from(userProfiles)
      .leftJoin(usersTable, eq(userProfiles.userId, usersTable.id))
      .where(
        and(
          eq(userProfiles.userId, userId),
          eq(userProfiles.organizationId, workspaceId),
          eq(userProfiles.isActive, true)
        )
      )
      .limit(1);

    if (!profile) {
      throw new HTTPException(404, { message: "User profile not found" });
    }

    return {
      id: profile.id,
      workspace_id: profile.workspace_id!,
      email: profile.email!,
      first_name: profile.first_name!,
      last_name: profile.last_name!,
      role: profile.role! as
        | "owner"
        | "admin"
        | "manager"
        | "member"
        | "viewer",
      phone: profile.phone || undefined,
      license_number: profile.license_number || undefined,
      bio: profile.bio || undefined,
      avatar_url: profile.avatar_url || undefined,
      specialties: profile.specialties || [],
      is_active: profile.is_active,
      invited_at: profile.invited_at || undefined,
      joined_at: profile.joined_at || undefined,
      invited_by: profile.invited_by || undefined,
      preferences: profile.preferences as UserPreferences,
      last_login_at: profile.last_login_at || undefined,
      created_at: profile.created_at,
      updated_at: profile.updated_at,
    };
  }

  static async updateUserProfile(
    userId: string,
    workspaceId: string,
    updateData: UpdateProfileData
  ) {
    const operations: OperationsCompleted = {
      profileUpdated: false,
    };
    const warnings: string[] = [];
    let requiresReauth = false;

    // Get current profile
    const currentProfile = await this.getUserProfile(userId, workspaceId);

    // Handle avatar file update
    let avatarUrl = currentProfile.avatar_url;
    if (updateData.avatarFileId) {
      avatarUrl = await this.handleAvatarUpdate(
        updateData.avatarFileId,
        workspaceId,
        userId
      );
      operations.avatarUpdated = true;
    }

    // Handle password change
    if (updateData.newPassword) {
      if (!updateData.currentPassword) {
        throw new HTTPException(400, {
          message: "Current password is required to change password",
        });
      }
      if (updateData.newPassword !== updateData.confirmPassword) {
        throw new HTTPException(400, {
          message: "New password and confirmation do not match",
        });
      }

      await this.changePassword(
        userId,
        updateData.currentPassword,
        updateData.newPassword
      );
      operations.passwordChanged = true;
      requiresReauth = true;
    }

    // Handle email change
    if (updateData.newEmail && updateData.newEmail !== currentProfile.email) {
      if (!updateData.currentPassword) {
        throw new HTTPException(400, {
          message: "Current password is required to change email",
        });
      }

      await this.initiateEmailChange(
        userId,
        updateData.newEmail,
        updateData.currentPassword
      );
      operations.emailChangeInitiated = true;
      operations.emailVerificationSent = true;
      warnings.push(
        "Email change initiated. Please check your new email for verification."
      );
    }

    // Merge preferences
    let updatedPreferences = currentProfile.preferences;
    if (updateData.preferences) {
      updatedPreferences = {
        notifications: {
          ...currentProfile.preferences.notifications,
          ...updateData.preferences.notifications,
        },
        display: {
          ...currentProfile.preferences.display,
          ...updateData.preferences.display,
        },
        privacy: {
          ...currentProfile.preferences.privacy,
          ...updateData.preferences.privacy,
        },
      };
    }

    // Update profile in database
    const updateFields: any = {
      updatedAt: new Date().toISOString(),
    };

    if (updateData.firstName !== undefined)
      updateFields.firstName = updateData.firstName;
    if (updateData.lastName !== undefined)
      updateFields.lastName = updateData.lastName;
    if (updateData.phone !== undefined) updateFields.phone = updateData.phone;
    if (updateData.licenseNumber !== undefined)
      updateFields.licenseNumber = updateData.licenseNumber;
    if (updateData.bio !== undefined) updateFields.bio = updateData.bio;
    if (updateData.specialties !== undefined)
      updateFields.specialties = updateData.specialties;
    if (avatarUrl !== currentProfile.avatar_url)
      updateFields.__avatarChanged = true;
    if (updateData.preferences) updateFields.preferences = updatedPreferences;

    if (Object.keys(updateFields).length > 1) {
      // More than just updatedAt
      await db
        .update(userProfiles)
        .set(Object.fromEntries(Object.entries(updateFields).filter(([k]) => k !== "__avatarChanged")))
        .where(
          and(
            eq(userProfiles.userId, userId),
            eq(userProfiles.organizationId, workspaceId)
          )
        );

      operations.profileUpdated = true;
    }

    // Update avatar on built-in user table if changed
    if (updateFields.__avatarChanged) {
      await db
        .update(usersTable)
        .set({ image: avatarUrl })
        .where(eq(usersTable.id, userId));
    }

    // Get updated profile
    const updatedProfile = await this.getUserProfile(userId, workspaceId);

    return {
      ...updatedProfile,
      operations_completed: operations,
      requires_reauth: requiresReauth || undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  private static async handleAvatarUpdate(
    fileId: string,
    workspaceId: string,
    userId: string
  ): Promise<string | undefined> {
    // Verify the file exists and belongs to the user
    const [file] = await db
      .select()
      .from(files)
      .where(
        and(
          eq(files.id, fileId),
          eq(files.organizationId, workspaceId),
          eq(files.uploadedBy, userId),
          eq(files.fileType, "avatar")
        )
      )
      .limit(1);

    if (!file) {
      throw new HTTPException(400, {
        message: "Avatar file not found or access denied",
      });
    }

    // For local storage, use public URL when available
    if (file.isPublic && file.storageUrl) {
      return file.storageUrl;
    }
    return undefined;
  }

  private static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ) {
    // Defer to Better Auth password change endpoint on the client; not handled here
    throw new HTTPException(400, { message: "Password change not supported in backend. Use auth endpoint." });
  }

  private static async initiateEmailChange(
    userId: string,
    newEmail: string,
    currentPassword: string
  ) {
    // Defer to Better Auth email change flow via auth endpoints
    throw new HTTPException(400, { message: "Email change not supported in backend. Use auth endpoint." });
  }

  static async verifyEmailChange(token: string, email: string) {
    try {
      // Defer to Better Auth verification; just update local profile if needed via separate webhook/event
      return {
        success: false,
        message: "Email verification handled by auth provider",
        email_updated: false,
      };
    } catch (error) {
      return {
        success: false,
        message: "Email verification failed",
        email_updated: false,
      };
    }
  }
}
