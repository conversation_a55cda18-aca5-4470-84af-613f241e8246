import { betterAuth } from "better-auth";
import { admin, organization } from "better-auth/plugins";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import db from "@/db";
import env from "@/env";
import { provisionUserTenant, ensureActiveOrganizationOnSession } from "@/lib/auth-hooks";

// Minimal Better Auth server instance. It will expose handlers we can mount under /api/auth.
export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
  }),
  baseURL: process.env.BETTER_AUTH_URL || `http://localhost:${env.PORT}`,
  secret: process.env.BETTER_AUTH_SECRET || "dev-secret-change-me",
  plugins: [admin(), organization()],
  // Hooks to mirror legacy behavior: after user creation, create org, member(owner), and profile
  // Casting to any to allow forward-compat hooks without tight coupling to library types
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  databaseHooks: {
    user: {
      create: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        after: async (payload: any) => {
          try {
            const newUser = payload?.user ?? payload;
            await provisionUserTenant(auth, newUser);
          } catch {
            // Best-effort hook; do not block auth flow on ancillary record creation
          }
        },
      },
    },
    session: {
      create: {
        // Ensure a default active organization is set on session creation
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        before: async (payload: any) => {
          try {
            await ensureActiveOrganizationOnSession(payload);
          } catch {
            // Non-fatal
          }
        },
      },
    },
  } as any,
});

export type BetterAuth = typeof auth;


