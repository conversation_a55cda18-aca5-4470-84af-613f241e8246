import db from "@/db";
import { member, userProfiles } from "@/db/schema";
import { eq } from "drizzle-orm";

interface MinimalUser {
  id: string;
  name?: string | null;
  email?: string | null;
}

// Create organization via Better Auth API, and create user profile for the new user
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function provisionUserTenant(authInstance: any, newUser: MinimalUser): Promise<string | undefined> {
  if (!newUser?.id) return undefined;

  const baseName =
    (newUser?.name || undefined) ??
    (typeof newUser?.email === "string" ? newUser.email.split("@")[0] : "My Organization");

  const metadata = { plan: "trial", status: "trial" };

  // Use Better Auth built-in API to create organization and membership
  // Prefer returning id from Better Auth instead of DB lookup
  // Response shape can vary across versions, so access defensively
  const createdOrg: any = await authInstance.api.createOrganization({
    body: {
      name: baseName || "My Organization",
      metadata,
      userId: newUser.id,
      keepCurrentActiveOrganization: false,
    },
  });

  const organizationId: string | undefined = createdOrg?.data?.id ?? createdOrg?.id ?? createdOrg?.organization?.id;

  if (organizationId) {
    // Create user profile linked to this organization
    await db.insert(userProfiles).values({
      userId: newUser.id,
      organizationId,
      isActive: true,
      joinedAt: new Date().toISOString(),
      displayName: (newUser?.name || undefined) ?? undefined,
    });
  }

  return organizationId;
}

// Ensure session payload has an activeOrganizationId set based on membership
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function ensureActiveOrganizationOnSession(payload: any): Promise<void> {
  const userId: string | undefined = payload?.session?.userId ?? payload?.user?.id ?? payload?.userId;
  if (!userId) return;

  if (payload?.session?.activeOrganizationId) return;

  const rows = await db
    .select({ organizationId: member.organizationId })
    .from(member)
    .where(eq(member.userId, userId))
    .limit(1);

  const firstOrgId = rows?.[0]?.organizationId;
  if (firstOrgId && payload.session) {
    payload.session.activeOrganizationId = firstOrgId;
  }
}


